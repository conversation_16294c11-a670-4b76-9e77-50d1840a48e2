'use client'

import { useRouter } from 'next/navigation'
import { useCallback } from 'react'
import {
  isPWA,
  navigatePWA,
  openPWA,
  routerPushPWA,
  getLinkTarget,
  createPWALinkHandler,
} from '@/utils/pwaNavigation'

/**
 * Hook for PWA-aware navigation
 * Provides methods that handle navigation appropriately based on PWA context
 */
export const usePWANavigation = () => {
  const router = useRouter()

  /**
   * Navigate to a URL with PWA-aware behavior
   */
  const navigate = useCallback(
    (
      url: string,
      options: {
        openInNewTab?: boolean
        replace?: boolean
        external?: boolean
      } = {}
    ) => {
      navigatePWA(url, options)
    },
    []
  )

  /**
   * Router push with PWA awareness
   */
  const push = useCallback(
    (
      url: string,
      options: {
        openInNewTab?: boolean
        replace?: boolean
      } = {}
    ) => {
      routerPushPWA(router, url, options)
    },
    [router]
  )

  /**
   * Router replace with PWA awareness
   */
  const replace = useCallback(
    (url: string) => {
      routerPushPWA(router, url, { replace: true })
    },
    [router]
  )

  /**
   * Open URL with PWA-aware behavior
   * This is a drop-in replacement for window.open
   */
  const open = useCallback(
    (
      url: string,
      target: string = '_blank',
      features?: string
    ): Window | null => {
      return openPWA(url, target, features)
    },
    []
  )

  /**
   * Open settings page with appropriate behavior
   * Handles the specific case of settings pages that should open in new tabs
   */
  const openSettings = useCallback(
    (settingsPath: string) => {
      const runningAsPWA = isPWA()

      if (runningAsPWA) {
        // In PWA mode, navigate within the same context
        router.push(settingsPath)
      } else {
        // In browser mode, open in new tab as originally intended
        window.open(settingsPath, '_blank', 'noopener,noreferrer')
      }
    },
    [router]
  )

  /**
   * Get appropriate link target based on PWA context
   */
  const getTarget = useCallback(
    (url: string, defaultTarget: string = '_self'): string => {
      return getLinkTarget(url, defaultTarget)
    },
    []
  )

  /**
   * Create a link click handler with PWA awareness
   */
  const createLinkHandler = useCallback(
    (
      url: string,
      options: {
        openInNewTab?: boolean
        replace?: boolean
        external?: boolean
        onClick?: () => void
      } = {}
    ) => {
      return createPWALinkHandler(url, options)
    },
    []
  )

  /**
   * Check if currently running as PWA
   */
  const isRunningAsPWA = useCallback(() => {
    return isPWA()
  }, [])

  return {
    navigate,
    push,
    replace,
    open,
    openSettings,
    getTarget,
    createLinkHandler,
    isRunningAsPWA,
    // Convenience methods
    isPWA: isRunningAsPWA,
  }
}

export default usePWANavigation
