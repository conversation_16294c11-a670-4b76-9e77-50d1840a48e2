'use client'

import Link from 'next/link'
import React from 'react'
import { usePWANavigation } from '@/hooks/usePWANavigation'

interface PWALinkProps {
  href: string
  children: React.ReactNode
  className?: string
  target?: '_self' | '_blank' | '_parent' | '_top'
  external?: boolean
  openInNewTab?: boolean
  replace?: boolean
  prefetch?: boolean
  onClick?: () => void
  style?: React.CSSProperties
  'aria-label'?: string
  role?: string
  id?: string
}

/**
 * PWA-aware Link component
 * Automatically handles navigation behavior based on PWA context
 */
export const PWALink: React.FC<PWALinkProps> = ({
  href,
  children,
  className,
  target = '_self',
  external = false,
  openInNewTab = false,
  replace = false,
  prefetch = true,
  onClick,
  style,
  'aria-label': ariaLabel,
  role,
  id,
}) => {
  const { getTarget, createLinkHandler, isRunningAsPWA } = usePWANavigation()

  // Determine if this should open in new tab
  const shouldOpenInNewTab = openInNewTab || target === '_blank'
  
  // Get appropriate target based on PWA context
  const linkTarget = getTarget(href, target)
  
  // Create PWA-aware click handler
  const handleClick = createLinkHandler(href, {
    openInNewTab: shouldOpenInNewTab,
    replace,
    external,
    onClick,
  })

  // For external links or when explicitly opening in new tab in non-PWA context
  if (external || (shouldOpenInNewTab && !isRunningAsPWA())) {
    return (
      <a
        href={href}
        className={className}
        target={linkTarget}
        rel={linkTarget === '_blank' ? 'noopener noreferrer' : undefined}
        onClick={handleClick}
        style={style}
        aria-label={ariaLabel}
        role={role}
        id={id}
      >
        {children}
      </a>
    )
  }

  // For internal links, use Next.js Link with PWA awareness
  return (
    <Link
      href={href}
      className={className}
      prefetch={prefetch}
      replace={replace}
      onClick={handleClick}
      style={style}
      aria-label={ariaLabel}
      role={role}
      id={id}
    >
      {children}
    </Link>
  )
}

/**
 * PWA-aware Settings Link component
 * Specifically designed for settings pages that should open appropriately in PWA context
 */
export const PWASettingsLink: React.FC<Omit<PWALinkProps, 'external' | 'openInNewTab'>> = ({
  href,
  children,
  ...props
}) => {
  const { isRunningAsPWA } = usePWANavigation()
  
  return (
    <PWALink
      href={href}
      openInNewTab={!isRunningAsPWA()}
      {...props}
    >
      {children}
    </PWALink>
  )
}

/**
 * PWA-aware External Link component
 * Always opens external links in new tab with proper security attributes
 */
export const PWAExternalLink: React.FC<Omit<PWALinkProps, 'external' | 'target'>> = ({
  href,
  children,
  ...props
}) => {
  return (
    <PWALink
      href={href}
      external={true}
      target="_blank"
      {...props}
    >
      {children}
    </PWALink>
  )
}

export default PWALink
