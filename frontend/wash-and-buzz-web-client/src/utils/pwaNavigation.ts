/**
 * PWA Navigation Utilities
 * Simplified utilities for essential PWA functionality
 */

/**
 * Check if the app is running as a PWA
 * @returns True if running as PWA, false otherwise
 */
export const isPWA = (): boolean => {
  if (typeof window === 'undefined') return false

  // Check if running in standalone mode (installed PWA)
  if (window.matchMedia('(display-mode: standalone)').matches) {
    return true
  }

  // Check for iOS Safari standalone mode
  if ((window.navigator as any).standalone === true) {
    return true
  }

  // Check for minimal-ui or fullscreen modes
  if (
    window.matchMedia('(display-mode: minimal-ui)').matches ||
    window.matchMedia('(display-mode: fullscreen)').matches
  ) {
    return true
  }

  return false
}

/**
 * Open settings page with appropriate PWA behavior
 * This is the main function needed for the current implementation
 * @param settingsPath - The path to the settings page
 */
export const openSettings = (settingsPath: string): void => {
  if (typeof window === 'undefined') return

  const runningAsPWA = isPWA()

  if (runningAsPWA) {
    // In PWA mode, navigate within the same context
    window.location.href = settingsPath
  } else {
    // In browser mode, open in new tab as originally intended
    window.open(settingsPath, '_blank', 'noopener,noreferrer')
  }
}
