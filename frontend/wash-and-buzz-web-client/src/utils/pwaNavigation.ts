/**
 * PWA Navigation Utilities
 * Handles navigation behavior for PWA, including new tab/window management
 */

/**
 * Check if the app is running as a PWA
 */
export const isPWA = (): boolean => {
  if (typeof window === 'undefined') return false
  
  // Check if running in standalone mode (installed PWA)
  if (window.matchMedia('(display-mode: standalone)').matches) {
    return true
  }
  
  // Check for iOS Safari standalone mode
  if ((window.navigator as any).standalone === true) {
    return true
  }
  
  // Check for minimal-ui or fullscreen modes
  if (
    window.matchMedia('(display-mode: minimal-ui)').matches ||
    window.matchMedia('(display-mode: fullscreen)').matches
  ) {
    return true
  }
  
  return false
}

/**
 * Check if a URL is within the app's scope
 */
export const isInternalUrl = (url: string): boolean => {
  if (typeof window === 'undefined') return true
  
  try {
    const targetUrl = new URL(url, window.location.origin)
    const currentOrigin = window.location.origin
    
    // Check if it's the same origin
    return targetUrl.origin === currentOrigin
  } catch {
    // If URL parsing fails, assume it's a relative URL (internal)
    return !url.startsWith('http')
  }
}

/**
 * Enhanced navigation function for PWA
 * Handles both internal navigation and external links appropriately
 */
export const navigatePWA = (
  url: string,
  options: {
    openInNewTab?: boolean
    replace?: boolean
    external?: boolean
  } = {}
): void => {
  const { openInNewTab = false, replace = false, external = false } = options
  
  if (typeof window === 'undefined') return
  
  const isInternal = !external && isInternalUrl(url)
  const runningAsPWA = isPWA()
  
  // Handle external URLs
  if (!isInternal) {
    window.open(url, '_blank', 'noopener,noreferrer')
    return
  }
  
  // Handle internal URLs
  if (openInNewTab) {
    if (runningAsPWA) {
      // In PWA mode, open internal URLs in the same app context
      // but create a new navigation stack
      if (replace) {
        window.location.replace(url)
      } else {
        window.location.href = url
      }
    } else {
      // In browser mode, open in new tab as requested
      window.open(url, '_blank', 'noopener,noreferrer')
    }
  } else {
    // Normal navigation
    if (replace) {
      window.location.replace(url)
    } else {
      window.location.href = url
    }
  }
}

/**
 * PWA-aware window.open replacement
 * Automatically handles PWA context and internal/external URLs
 */
export const openPWA = (
  url: string,
  target: string = '_blank',
  features?: string
): Window | null => {
  if (typeof window === 'undefined') return null
  
  const isInternal = isInternalUrl(url)
  const runningAsPWA = isPWA()
  
  // If it's an internal URL and we're running as PWA
  if (isInternal && runningAsPWA && target === '_blank') {
    // Navigate within the same PWA context
    navigatePWA(url, { openInNewTab: true })
    return null
  }
  
  // For external URLs or non-PWA context, use standard window.open
  return window.open(url, target, features)
}

/**
 * Get the appropriate target for links based on PWA context
 */
export const getLinkTarget = (
  url: string,
  defaultTarget: string = '_self'
): string => {
  if (typeof window === 'undefined') return defaultTarget
  
  const isInternal = isInternalUrl(url)
  const runningAsPWA = isPWA()
  
  // For external URLs, always open in new tab
  if (!isInternal) {
    return '_blank'
  }
  
  // For internal URLs in PWA, prefer same window navigation
  if (runningAsPWA && defaultTarget === '_blank') {
    return '_self'
  }
  
  return defaultTarget
}

/**
 * Enhanced router push for PWA context
 * Works with Next.js router but handles PWA-specific behavior
 */
export const routerPushPWA = (
  router: any,
  url: string,
  options: {
    openInNewTab?: boolean
    replace?: boolean
  } = {}
): void => {
  const { openInNewTab = false, replace = false } = options
  
  if (typeof window === 'undefined') {
    // Server-side, use normal router
    if (replace) {
      router.replace(url)
    } else {
      router.push(url)
    }
    return
  }
  
  const runningAsPWA = isPWA()
  const isInternal = isInternalUrl(url)
  
  if (!isInternal) {
    // External URL
    window.open(url, '_blank', 'noopener,noreferrer')
    return
  }
  
  if (openInNewTab && !runningAsPWA) {
    // Open in new tab only if not PWA
    window.open(url, '_blank', 'noopener,noreferrer')
    return
  }
  
  // Use Next.js router for internal navigation
  if (replace) {
    router.replace(url)
  } else {
    router.push(url)
  }
}

/**
 * PWA-specific event handlers for links
 */
export const createPWALinkHandler = (
  url: string,
  options: {
    openInNewTab?: boolean
    replace?: boolean
    external?: boolean
    onClick?: () => void
  } = {}
) => {
  return (event: React.MouseEvent<HTMLAnchorElement>) => {
    const { openInNewTab = false, external = false, onClick } = options
    
    // Call custom onClick if provided
    if (onClick) {
      onClick()
    }
    
    // Handle PWA navigation
    if (openInNewTab || external) {
      event.preventDefault()
      navigatePWA(url, { openInNewTab: true, external })
    }
    // For normal navigation, let the default behavior handle it
  }
}

/**
 * Check if the current page was opened in a new context
 */
export const isNewContext = (): boolean => {
  if (typeof window === 'undefined') return false
  
  try {
    // Check if there's a referrer from the same origin
    return !document.referrer || new URL(document.referrer).origin !== window.location.origin
  } catch {
    return false
  }
}

/**
 * Get PWA display mode
 */
export const getPWADisplayMode = (): string => {
  if (typeof window === 'undefined') return 'browser'
  
  if (window.matchMedia('(display-mode: standalone)').matches) {
    return 'standalone'
  }
  if (window.matchMedia('(display-mode: minimal-ui)').matches) {
    return 'minimal-ui'
  }
  if (window.matchMedia('(display-mode: fullscreen)').matches) {
    return 'fullscreen'
  }
  if ((window.navigator as any).standalone === true) {
    return 'standalone-ios'
  }
  
  return 'browser'
}
